@echo off
echo ========================================
echo    🔮 神秘占卜馆 启动脚本 🔮
echo ========================================
echo.

echo 正在检查Docker环境...
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: 未找到Docker，请先安装Docker Desktop
    pause
    exit /b 1
)

docker-compose --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: 未找到Docker Compose，请确保Docker Desktop已正确安装
    pause
    exit /b 1
)

echo ✅ Docker环境检查通过
echo.

echo 正在启动占卜游戏服务...
docker-compose up -d

if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo    🎉 占卜游戏启动成功！ 🎉
    echo ========================================
    echo.
    echo 📱 占卜游戏地址: http://localhost:8082
    echo 🗄️  数据库管理: http://localhost:8081
    echo.
    echo 数据库连接信息:
    echo   主机: localhost:3307
    echo   数据库: fortune_db
    echo   用户名: fortune_user
    echo   密码: fortune_pass
    echo.
    echo phpMyAdmin登录信息:
    echo   用户名: root
    echo   密码: root_password
    echo.
    echo 💡 提示: 首次启动可能需要几分钟来下载镜像和初始化数据库
    echo.
    echo 按任意键打开占卜游戏...
    pause >nul
    start http://localhost:8082
) else (
    echo.
    echo ❌ 启动失败，请检查错误信息
    echo.
    echo 常见解决方案:
    echo 1. 确保端口8082、8081、3307未被占用
    echo 2. 确保Docker Desktop正在运行
    echo 3. 尝试运行: docker-compose down 然后重新启动
    echo.
    pause
)

echo.
echo 其他有用的命令:
echo   停止服务: docker-compose down
echo   查看日志: docker-compose logs
echo   重启服务: docker-compose restart
echo.
pause
