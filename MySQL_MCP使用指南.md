# MySQL MCP 服务器使用指南

## 🎉 恭喜！MySQL MCP 服务器已成功安装

你现在可以直接在 Cursor 中通过 AI 助手操作 MySQL 数据库，无需手动编写 SQL 连接代码或使用复杂的数据库管理工具。

## 📋 已配置信息

- **MCP 服务器**: @dpflucas/mysql-mcp-server
- **客户端**: Cursor
- **配置文件**: very-pigeon-zXdvD4
- **状态**: ✅ 已安装并可用

## 🔗 数据库连接信息

- **服务器地址**: mysql320.phy.heteml.lan
- **用户名**: _yuzong
- **密码**: gs888888
- **端口**: 3306 (默认)

## 🚀 如何使用

### 1. 连接到 MySQL 数据库

在 Cursor 中直接对 AI 助手说：

```
请帮我连接到 MySQL 数据库 mysql320.phy.heteml.lan，用户名 _yuzong，密码 gs888888
```

### 2. 查看数据库结构

```
请显示所有数据库
```

```
请显示当前数据库中的所有表
```

```
请显示表 [表名] 的结构
```

### 3. 数据查询操作

```
请查询表 users 中的所有数据
```

```
请执行 SQL 查询：SELECT * FROM products WHERE price > 100
```

```
请统计表 orders 中的记录数量
```

### 4. 数据插入操作

```
请向 users 表插入一条新记录：name='张三', email='<EMAIL>'
```

```
请批量插入测试数据到 products 表
```

### 5. 数据更新操作

```
请更新 users 表中 id=1 的用户邮箱为 <EMAIL>
```

```
请将所有价格小于 50 的产品状态设为 'discontinued'
```

### 6. 数据删除操作

```
请删除 users 表中 id=5 的记录
```

```
请删除所有创建时间超过 30 天的临时记录
```

### 7. 表结构管理

```
请创建一个新表 customers，包含 id、name、email、phone、created_at 字段
```

```
请为 products 表添加一个 description 字段
```

```
请删除表 temp_data
```

## 💡 高级使用技巧

### 复杂查询
```
请查询每个分类下销量最高的 3 个产品
```

```
请统计过去 30 天每天的订单数量和总金额
```

### 数据分析
```
请分析用户注册趋势，按月统计
```

```
请找出最受欢迎的产品类别
```

### 数据库维护
```
请检查数据库中是否有重复的邮箱地址
```

```
请优化 products 表的查询性能
```

### 备份和恢复
```
请导出 users 表的数据
```

```
请创建数据库备份
```

## 🔧 故障排除

### 如果连接失败
1. 检查数据库服务器地址是否正确
2. 确认用户名和密码是否正确
3. 检查网络连接是否正常
4. 确认数据库服务器是否允许远程连接
5. 检查防火墙设置

### 如果查询失败
1. 检查 SQL 语法是否正确
2. 确认表名和字段名是否存在
3. 检查用户权限是否足够
4. 确认数据类型是否匹配

### 如果 MCP 不工作
1. 重启 Cursor
2. 检查 MCP 服务器是否正确安装
3. 确认配置文件是否正确

## 📚 实用示例

### 创建一个完整的用户系统

1. **创建用户表**：
   ```
   请创建用户表，包含以下字段：
   - id (主键，自增)
   - username (用户名，唯一)
   - email (邮箱，唯一)
   - password_hash (密码哈希)
   - created_at (创建时间)
   - updated_at (更新时间)
   ```

2. **插入测试数据**：
   ```
   请向用户表插入 5 条测试数据
   ```

3. **查询活跃用户**：
   ```
   请查询最近 7 天注册的用户
   ```

### 电商数据分析

1. **销售统计**：
   ```
   请统计每个月的销售额和订单数量
   ```

2. **热门产品**：
   ```
   请找出销量前 10 的产品
   ```

3. **用户行为分析**：
   ```
   请分析用户的购买频率和平均订单金额
   ```

## 🌟 MySQL MCP 的优势

- **🚀 简单易用**: 无需学习复杂的 SQL 语法
- **🤖 智能理解**: AI 自动生成和优化 SQL 查询
- **⚡ 快速操作**: 一句话完成复杂的数据库操作
- **🔄 实时反馈**: 立即看到查询结果
- **📱 集成体验**: 直接在 Cursor 中完成所有数据库操作
- **🛡️ 安全可靠**: 自动处理 SQL 注入防护

## 🎯 开始使用

现在就在 Cursor 中试试吧！直接对 AI 助手说：

```
请帮我连接到 MySQL 数据库并显示所有表
```

## 🔒 安全注意事项

1. **不要在生产环境中使用测试密码**
2. **定期备份重要数据**
3. **限制数据库用户权限**
4. **使用 SSL 连接（如果可用）**
5. **监控数据库访问日志**

---

**创建时间**: 2025-05-27  
**适用于**: Cursor + MySQL MCP 服务器  
**版本**: 1.0 