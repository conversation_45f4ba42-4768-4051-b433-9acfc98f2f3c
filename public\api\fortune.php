<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}

require_once '../../classes/Fortune.php';

try {
    $fortune = new Fortune();
    $action = $_GET['action'] ?? $_POST['action'] ?? '';

    switch ($action) {
        case 'getTypes':
            $types = $fortune->getFortuneTypes();
            echo json_encode([
                'success' => true,
                'data' => $types
            ]);
            break;

        case 'getQuestions':
            $type_id = $_GET['type_id'] ?? $_POST['type_id'] ?? 0;
            if ($type_id) {
                $questions = $fortune->getQuestionsByType($type_id);
                echo json_encode([
                    'success' => true,
                    'data' => $questions
                ]);
            } else {
                echo json_encode([
                    'success' => false,
                    'message' => '请提供占卜类型ID'
                ]);
            }
            break;

        case 'getFortune':
            $type_id = $_POST['type_id'] ?? 0;
            $user_name = $_POST['user_name'] ?? null;
            $birth_date = $_POST['birth_date'] ?? null;
            
            if ($type_id) {
                // 添加一些延迟来模拟占卜过程
                sleep(2);
                
                $result = $fortune->getFortune($type_id, $user_name, $birth_date);
                if ($result) {
                    echo json_encode([
                        'success' => true,
                        'data' => $result
                    ]);
                } else {
                    echo json_encode([
                        'success' => false,
                        'message' => '占卜失败，请重试'
                    ]);
                }
            } else {
                echo json_encode([
                    'success' => false,
                    'message' => '请选择占卜类型'
                ]);
            }
            break;

        case 'getHistory':
            $user_name = $_GET['user_name'] ?? '';
            if ($user_name) {
                $history = $fortune->getUserHistory($user_name);
                echo json_encode([
                    'success' => true,
                    'data' => $history
                ]);
            } else {
                echo json_encode([
                    'success' => false,
                    'message' => '请提供用户名'
                ]);
            }
            break;

        case 'getTodayStats':
            $stats = $fortune->getTodayStats();
            echo json_encode([
                'success' => true,
                'data' => $stats
            ]);
            break;

        case 'getResult':
            $result_id = $_GET['result_id'] ?? 0;
            if ($result_id) {
                $result = $fortune->getFortuneResult($result_id);
                echo json_encode([
                    'success' => true,
                    'data' => $result
                ]);
            } else {
                echo json_encode([
                    'success' => false,
                    'message' => '请提供结果ID'
                ]);
            }
            break;

        default:
            echo json_encode([
                'success' => false,
                'message' => '未知的操作'
            ]);
            break;
    }

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => '服务器错误: ' . $e->getMessage()
    ]);
}
?>
