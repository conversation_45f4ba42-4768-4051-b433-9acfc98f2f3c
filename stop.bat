@echo off
echo ========================================
echo    🔮 神秘占卜馆 停止脚本 🔮
echo ========================================
echo.

echo 正在停止占卜游戏服务...
docker-compose down

if %errorlevel% equ 0 (
    echo.
    echo ✅ 占卜游戏服务已成功停止
    echo.
    echo 💡 提示:
    echo   - 数据已保存，下次启动时会保留
    echo   - 如需完全清理，请运行: docker-compose down -v
    echo   - 重新启动请运行: start.bat
) else (
    echo.
    echo ❌ 停止服务时出现错误
    echo 请检查Docker是否正在运行
)

echo.
pause
