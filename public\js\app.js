// 全局变量
let selectedType = null;
let currentQuestions = [];
let userAnswers = {};

// DOM元素
const fortuneTypesContainer = document.getElementById('fortuneTypes');
const questionsSection = document.getElementById('fortuneQuestions');
const questionsList = document.getElementById('questionsList');
const startFortuneBtn = document.getElementById('startFortune');
const resultSection = document.getElementById('fortuneResult');
const resultContent = document.getElementById('resultContent');
const newFortuneBtn = document.getElementById('newFortune');
const shareResultBtn = document.getElementById('shareResult');
const historyList = document.getElementById('historyList');
const todayStats = document.getElementById('todayStats');
const loadingOverlay = document.getElementById('loadingOverlay');

// 初始化应用
document.addEventListener('DOMContentLoaded', function() {
    loadFortuneTypes();
    loadTodayStats();
    
    // 检查是否有用户名，如果有则加载历史记录
    const userName = document.getElementById('userName').value;
    if (userName) {
        loadUserHistory(userName);
    }
    
    // 绑定事件
    startFortuneBtn.addEventListener('click', startFortune);
    newFortuneBtn.addEventListener('click', resetFortune);
    shareResultBtn.addEventListener('click', shareResult);
    
    // 监听用户名输入变化
    document.getElementById('userName').addEventListener('blur', function() {
        const userName = this.value.trim();
        if (userName) {
            loadUserHistory(userName);
        }
    });
});

// 加载占卜类型
async function loadFortuneTypes() {
    try {
        const response = await fetch('api/fortune.php?action=getTypes');
        const data = await response.json();
        
        if (data.success) {
            displayFortuneTypes(data.data);
        } else {
            showError('加载占卜类型失败');
        }
    } catch (error) {
        showError('网络错误，请稍后重试');
    }
}

// 显示占卜类型
function displayFortuneTypes(types) {
    fortuneTypesContainer.innerHTML = '';
    
    types.forEach(type => {
        const typeCard = document.createElement('button');
        typeCard.className = 'type-card';
        typeCard.innerHTML = `
            <h3>${type.name}</h3>
            <p>${type.description}</p>
        `;
        
        typeCard.addEventListener('click', () => selectFortuneType(type));
        fortuneTypesContainer.appendChild(typeCard);
    });
}

// 选择占卜类型
async function selectFortuneType(type) {
    // 移除之前的选中状态
    document.querySelectorAll('.type-card').forEach(card => {
        card.classList.remove('selected');
    });
    
    // 添加选中状态
    event.target.classList.add('selected');
    selectedType = type;
    
    // 加载相关问题
    await loadQuestions(type.id);
}

// 加载问题
async function loadQuestions(typeId) {
    try {
        const response = await fetch(`api/fortune.php?action=getQuestions&type_id=${typeId}`);
        const data = await response.json();
        
        if (data.success) {
            currentQuestions = data.data;
            displayQuestions(data.data);
            questionsSection.style.display = 'block';
            questionsSection.scrollIntoView({ behavior: 'smooth' });
        } else {
            showError('加载问题失败');
        }
    } catch (error) {
        showError('网络错误，请稍后重试');
    }
}

// 显示问题
function displayQuestions(questions) {
    questionsList.innerHTML = '';
    userAnswers = {};
    
    questions.forEach((question, index) => {
        const questionDiv = document.createElement('div');
        questionDiv.className = 'question-item';
        questionDiv.innerHTML = `
            <h4>问题 ${index + 1}: ${question.question}</h4>
            <div class="question-options">
                <button class="option-btn" data-question="${question.id}" data-answer="1">非常同意</button>
                <button class="option-btn" data-question="${question.id}" data-answer="2">同意</button>
                <button class="option-btn" data-question="${question.id}" data-answer="3">中立</button>
                <button class="option-btn" data-question="${question.id}" data-answer="4">不同意</button>
                <button class="option-btn" data-question="${question.id}" data-answer="5">非常不同意</button>
            </div>
        `;
        
        // 绑定选项点击事件
        questionDiv.querySelectorAll('.option-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const questionId = this.dataset.question;
                const answer = this.dataset.answer;
                
                // 移除同一问题的其他选中状态
                questionDiv.querySelectorAll('.option-btn').forEach(b => b.classList.remove('selected'));
                
                // 添加选中状态
                this.classList.add('selected');
                
                // 保存答案
                userAnswers[questionId] = answer;
                
                // 检查是否所有问题都已回答
                checkAllQuestionsAnswered();
            });
        });
        
        questionsList.appendChild(questionDiv);
    });
}

// 检查是否所有问题都已回答
function checkAllQuestionsAnswered() {
    const answeredCount = Object.keys(userAnswers).length;
    const totalQuestions = currentQuestions.length;
    
    if (answeredCount === totalQuestions) {
        startFortuneBtn.disabled = false;
        startFortuneBtn.textContent = '开始占卜';
    } else {
        startFortuneBtn.disabled = true;
        startFortuneBtn.textContent = `请回答所有问题 (${answeredCount}/${totalQuestions})`;
    }
}

// 开始占卜
async function startFortune() {
    if (!selectedType) {
        showError('请先选择占卜类型');
        return;
    }
    
    const userName = document.getElementById('userName').value.trim();
    const birthDate = document.getElementById('birthDate').value;
    
    // 显示加载动画
    showLoading(true);
    
    try {
        const formData = new FormData();
        formData.append('action', 'getFortune');
        formData.append('type_id', selectedType.id);
        if (userName) formData.append('user_name', userName);
        if (birthDate) formData.append('birth_date', birthDate);
        
        const response = await fetch('api/fortune.php', {
            method: 'POST',
            body: formData
        });
        
        const data = await response.json();
        
        if (data.success) {
            displayResult(data.data);
            resultSection.style.display = 'block';
            resultSection.scrollIntoView({ behavior: 'smooth' });
            
            // 如果有用户名，重新加载历史记录
            if (userName) {
                loadUserHistory(userName);
            }
        } else {
            showError(data.message || '占卜失败，请重试');
        }
    } catch (error) {
        showError('网络错误，请稍后重试');
    } finally {
        showLoading(false);
    }
}

// 显示占卜结果
function displayResult(result) {
    resultContent.innerHTML = `
        <div class="result-header">
            <h3 class="result-title">${result.title}</h3>
            <span class="luck-level ${result.luck_level}">${result.luck_level}</span>
        </div>
        
        <div class="result-content-text">
            ${result.content}
        </div>
        
        <div class="result-advice">
            <h4>💡 建议</h4>
            <p>${result.advice}</p>
        </div>
        
        <div class="result-extras">
            <div class="extra-item">
                <strong>幸运数字</strong><br>
                ${result.lucky_number}
            </div>
            <div class="extra-item">
                <strong>幸运颜色</strong><br>
                ${result.lucky_color}
            </div>
            <div class="extra-item">
                <strong>占卜日期</strong><br>
                ${result.fortune_date}
            </div>
            <div class="extra-item">
                <strong>占卜类型</strong><br>
                ${result.type_name}
            </div>
        </div>
    `;
}

// 重新占卜
function resetFortune() {
    selectedType = null;
    currentQuestions = [];
    userAnswers = {};
    
    // 重置UI
    document.querySelectorAll('.type-card').forEach(card => {
        card.classList.remove('selected');
    });
    
    questionsSection.style.display = 'none';
    resultSection.style.display = 'none';
    startFortuneBtn.disabled = true;
    startFortuneBtn.textContent = '请先回答问题';
    
    // 滚动到顶部
    window.scrollTo({ top: 0, behavior: 'smooth' });
}

// 分享结果
function shareResult() {
    const resultText = resultContent.textContent;
    
    if (navigator.share) {
        navigator.share({
            title: '我的占卜结果',
            text: resultText,
            url: window.location.href
        });
    } else {
        // 复制到剪贴板
        navigator.clipboard.writeText(resultText).then(() => {
            alert('结果已复制到剪贴板！');
        }).catch(() => {
            alert('分享功能暂不可用');
        });
    }
}

// 加载用户历史记录
async function loadUserHistory(userName) {
    try {
        const response = await fetch(`api/fortune.php?action=getHistory&user_name=${encodeURIComponent(userName)}`);
        const data = await response.json();
        
        if (data.success) {
            displayHistory(data.data);
        }
    } catch (error) {
        console.error('加载历史记录失败:', error);
    }
}

// 显示历史记录
function displayHistory(history) {
    if (history.length === 0) {
        historyList.innerHTML = '<p style="text-align: center; color: #718096;">暂无占卜记录</p>';
        return;
    }
    
    historyList.innerHTML = '';
    
    history.forEach(record => {
        const historyItem = document.createElement('div');
        historyItem.className = 'history-item';
        historyItem.innerHTML = `
            <div class="history-info">
                <h4>${record.type_name} - ${record.title}</h4>
                <p>运势: ${record.luck_level} | 日期: ${record.fortune_date}</p>
            </div>
            <span class="luck-level ${record.luck_level}">${record.luck_level}</span>
        `;
        
        historyList.appendChild(historyItem);
    });
}

// 加载今日统计
async function loadTodayStats() {
    try {
        const response = await fetch('api/fortune.php?action=getTodayStats');
        const data = await response.json();
        
        if (data.success) {
            displayTodayStats(data.data);
        }
    } catch (error) {
        console.error('加载统计数据失败:', error);
    }
}

// 显示今日统计
function displayTodayStats(stats) {
    if (stats.length === 0) {
        todayStats.innerHTML = '<p style="text-align: center; color: #718096;">今日暂无占卜记录</p>';
        return;
    }
    
    todayStats.innerHTML = '';
    
    stats.forEach(stat => {
        const statItem = document.createElement('div');
        statItem.className = 'stat-item';
        statItem.innerHTML = `
            <h4>${stat.name}</h4>
            <div class="stat-number">${stat.count}</div>
            <p>次占卜</p>
        `;
        
        todayStats.appendChild(statItem);
    });
}

// 显示/隐藏加载动画
function showLoading(show) {
    loadingOverlay.style.display = show ? 'flex' : 'none';
}

// 显示错误信息
function showError(message) {
    alert(message);
}
