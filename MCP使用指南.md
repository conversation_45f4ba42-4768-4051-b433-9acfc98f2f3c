# MCP FTP 服务器使用指南

## 🎉 恭喜！MCP FTP 服务器已成功安装

你现在可以直接在 Cursor 中通过 AI 助手操作 FTP 服务器，无需手动编写脚本或使用复杂的命令行工具。

## 📋 已配置信息

- **MCP 服务器**: @alxspiker/mcp-server-ftp
- **客户端**: Cursor
- **配置文件**: very-pigeon-zXdvD4
- **状态**: ✅ 已安装并可用

## 🚀 如何使用

### 1. 连接到 FTP 服务器

在 Cursor 中直接对 AI 助手说：

```
请帮我连接到 FTP 服务器 ftp-myphp.heteml.net，用户名 myphp_yuzong，密码 gs888888
```

### 2. 上传 index.php 文件

```
请帮我上传 index.php 文件到 FTP 服务器的根目录
```

### 3. 查看 FTP 服务器文件列表

```
请显示 FTP 服务器上的所有文件
```

### 4. 下载文件

```
请从 FTP 服务器下载 [文件名] 到本地
```

### 5. 删除文件

```
请删除 FTP 服务器上的 [文件名]
```

### 6. 创建目录

```
请在 FTP 服务器上创建一个名为 [目录名] 的文件夹
```

## 💡 使用技巧

### 自然语言交互
你可以用自然语言描述你想要做的操作，AI 助手会自动转换为相应的 FTP 命令：

- "帮我把所有 PHP 文件上传到服务器"
- "检查服务器上是否有 index.php 文件"
- "备份服务器上的所有文件到本地"
- "清理服务器上的临时文件"

### 批量操作
```
请帮我上传当前目录下的所有 .php 文件到 FTP 服务器
```

### 文件同步
```
请比较本地和 FTP 服务器上的文件，告诉我哪些文件需要更新
```

## 🔧 故障排除

### 如果连接失败
1. 检查 FTP 服务器地址是否正确
2. 确认用户名和密码是否正确
3. 检查网络连接是否正常
4. 确认 FTP 服务器是否允许你的 IP 访问

### 如果上传失败
1. 检查本地文件是否存在
2. 确认你有写入权限
3. 检查服务器磁盘空间是否充足
4. 确认文件大小是否超过限制

### 如果 MCP 不工作
1. 重启 Cursor
2. 检查 MCP 服务器是否正确安装
3. 确认配置文件是否正确

## 📚 更多功能

### 文件权限管理
```
请设置 index.php 文件的权限为 644
```

### 目录操作
```
请切换到 public_html 目录
```

### 文件信息查看
```
请显示 index.php 文件的详细信息（大小、修改时间等）
```

## 🎯 实际使用示例

### 完整的网站部署流程

1. **连接服务器**：
   ```
   请连接到我的 FTP 服务器 ftp-myphp.heteml.net
   ```

2. **上传主页**：
   ```
   请上传 index.php 到服务器根目录
   ```

3. **验证上传**：
   ```
   请确认 index.php 文件已成功上传并显示文件大小
   ```

4. **设置权限**：
   ```
   请确保 index.php 文件有正确的权限
   ```

## 🌟 MCP 的优势

- **🚀 简单易用**: 无需学习 FTP 命令
- **🤖 智能理解**: AI 自动理解你的意图
- **⚡ 快速操作**: 一句话完成复杂操作
- **🔄 实时反馈**: 立即看到操作结果
- **📱 集成体验**: 直接在 Cursor 中完成所有操作

## 🎉 开始使用

现在就在 Cursor 中试试吧！直接对 AI 助手说：

```
请帮我连接到 FTP 服务器并上传 index.php 文件
```

---

**创建时间**: 2025-05-27  
**适用于**: Cursor + MCP FTP 服务器  
**版本**: 1.0 