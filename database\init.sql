-- 创建占卜游戏数据库表

-- 占卜类型表
CREATE TABLE IF NOT EXISTS fortune_types (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 占卜结果表
CREATE TABLE IF NOT EXISTS fortune_results (
    id INT AUTO_INCREMENT PRIMARY KEY,
    type_id INT,
    title VARCHAR(100) NOT NULL,
    content TEXT NOT NULL,
    luck_level ENUM('大吉', '中吉', '小吉', '吉', '半吉', '末吉', '末小吉', '凶', '小凶', '半凶', '末凶', '大凶') NOT NULL,
    advice TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (type_id) REFERENCES fortune_types(id)
);

-- 用户占卜记录表
CREATE TABLE IF NOT EXISTS user_fortunes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_name VARCHAR(50),
    user_birth_date DATE,
    fortune_type_id INT,
    fortune_result_id INT,
    fortune_date DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (fortune_type_id) REFERENCES fortune_types(id),
    FOREIGN KEY (fortune_result_id) REFERENCES fortune_results(id)
);

-- 占卜问题表
CREATE TABLE IF NOT EXISTS fortune_questions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    type_id INT,
    question TEXT NOT NULL,
    weight INT DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (type_id) REFERENCES fortune_types(id)
);
