# 大型CSV文件分批上传解决方案

## 问题描述
`データダウンロード_莫専用_20250526100701.csv` 文件大小为22MB，使用Shift-JIS编码，直接上传会遇到以下问题：
- 文件大小超过服务器限制
- 上传超时
- 内存不足
- 编码问题

## 解决方案
采用分批处理的方式：
1. **本地分割**：将大文件分割成小块
2. **批量上传**：上传所有文件块到服务器
3. **服务器合并**：在服务器端重新组合并处理

## 使用步骤

### 方法一：自动化批处理（推荐）
```bash
# 运行自动化脚本
process_large_csv.bat
```

### 方法二：手动执行
1. **分割文件**
   ```bash
   php split_large_csv.php
   ```

2. **上传文件块**
   ```bash
   php batch_upload_chunks.php
   ```

3. **访问服务器处理页面**
   ```
   https://189527.com/yuzong/process_chunks_on_server.php
   ```

## 脚本说明

### 1. split_large_csv.php
- **功能**：分割大型CSV文件
- **配置**：
  - `$sourceFile`：源文件名
  - `$chunkSize`：每个文件块的行数（默认1000行）
  - `$outputDir`：输出目录
- **特性**：
  - 自动检测Shift-JIS编码
  - 转换为UTF-8
  - 保留表头信息

### 2. batch_upload_chunks.php
- **功能**：批量上传文件块到FTP服务器
- **配置**：
  - FTP服务器信息
  - 远程目录路径
- **特性**：
  - 自动创建远程目录
  - 显示上传进度
  - 错误处理

### 3. process_chunks_on_server.php
- **功能**：服务器端处理文件块
- **特性**：
  - 自动创建数据表
  - 批量导入数据
  - 进度显示
  - 数据预览

## 配置参数

### 文件分割配置
```php
$sourceFile = 'データダウンロード_莫専用_20250526100701.csv';
$chunkSize = 1000; // 每个文件的行数
$outputDir = 'csv_chunks/';
```

### FTP配置
```php
$ftpServer = 'ftp-myphp.heteml.net';
$ftpUsername = 'myphp_yuzong';
$ftpPassword = 'gs888888';
$remoteDir = '/yuzong/csv_chunks/';
```

### 数据库配置
```php
$host = 'mysql320.phy.heteml.lan';
$username = '_yuzong';
$password = 'gs888888';
$database = '_yuzong';
$tableName = 'japanese_data';
```

## 预期结果

### 文件分割
- 22MB文件 → 约22个文件块（每个约1MB）
- 每个文件块包含1000行数据
- 自动处理Shift-JIS编码

### 数据导入
- 创建 `japanese_data` 表
- 导入所有数据记录
- 支持日文字符
- 提供数据预览

## 优势
1. **突破大小限制**：分割后每个文件块小于2MB
2. **稳定上传**：小文件上传更稳定
3. **断点续传**：可以重新上传失败的文件块
4. **编码处理**：自动处理Shift-JIS编码
5. **进度监控**：实时显示处理进度

## 注意事项
1. 确保PHP环境支持mbstring扩展
2. 检查FTP连接权限
3. 确保数据库连接正常
4. 预留足够的磁盘空间
5. 处理过程中不要中断

## 故障排除

### 常见问题
1. **编码问题**：确保文件是Shift-JIS编码
2. **FTP连接失败**：检查网络和凭据
3. **数据库连接失败**：验证数据库配置
4. **内存不足**：调整PHP内存限制

### 解决方案
```php
// 增加内存限制
ini_set('memory_limit', '512M');

// 增加执行时间
ini_set('max_execution_time', 300);
```

## 完成状态
- ✅ 分割脚本已创建
- ✅ 上传脚本已创建
- ✅ 服务器处理脚本已上传
- ✅ 自动化批处理脚本已创建
- ⏳ 等待执行分批上传

## 下一步
1. 运行 `process_large_csv.bat`
2. 等待处理完成
3. 访问服务器查看结果
4. 验证数据完整性 