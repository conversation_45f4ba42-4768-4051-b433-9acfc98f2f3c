<?php
require_once '../config/database.php';

class Fortune {
    private $conn;
    private $table_types = 'fortune_types';
    private $table_results = 'fortune_results';
    private $table_user_fortunes = 'user_fortunes';
    private $table_questions = 'fortune_questions';

    public function __construct() {
        $database = new Database();
        $this->conn = $database->getConnection();
    }

    // 获取所有占卜类型
    public function getFortuneTypes() {
        $query = "SELECT * FROM " . $this->table_types . " ORDER BY id";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // 根据类型获取问题
    public function getQuestionsByType($type_id) {
        $query = "SELECT * FROM " . $this->table_questions . " WHERE type_id = ? ORDER BY RAND() LIMIT 3";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(1, $type_id);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // 进行占卜
    public function getFortune($type_id, $user_name = null, $birth_date = null) {
        // 根据类型随机获取一个结果
        $query = "SELECT fr.*, ft.name as type_name 
                  FROM " . $this->table_results . " fr 
                  JOIN " . $this->table_types . " ft ON fr.type_id = ft.id 
                  WHERE fr.type_id = ? 
                  ORDER BY RAND() 
                  LIMIT 1";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(1, $type_id);
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($result) {
            // 记录用户占卜历史
            $this->saveUserFortune($user_name, $birth_date, $type_id, $result['id']);
            
            // 添加一些随机元素
            $result['lucky_number'] = rand(1, 100);
            $result['lucky_color'] = $this->getRandomColor();
            $result['fortune_date'] = date('Y-m-d');
        }

        return $result;
    }

    // 保存用户占卜记录
    private function saveUserFortune($user_name, $birth_date, $type_id, $result_id) {
        if ($user_name) {
            $query = "INSERT INTO " . $this->table_user_fortunes . " 
                      (user_name, user_birth_date, fortune_type_id, fortune_result_id, fortune_date) 
                      VALUES (?, ?, ?, ?, CURDATE())";
            
            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(1, $user_name);
            $stmt->bindParam(2, $birth_date);
            $stmt->bindParam(3, $type_id);
            $stmt->bindParam(4, $result_id);
            $stmt->execute();
        }
    }

    // 获取用户历史占卜记录
    public function getUserHistory($user_name, $limit = 10) {
        $query = "SELECT uf.*, ft.name as type_name, fr.title, fr.luck_level 
                  FROM " . $this->table_user_fortunes . " uf 
                  JOIN " . $this->table_types . " ft ON uf.fortune_type_id = ft.id 
                  JOIN " . $this->table_results . " fr ON uf.fortune_result_id = fr.id 
                  WHERE uf.user_name = ? 
                  ORDER BY uf.created_at DESC 
                  LIMIT ?";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(1, $user_name);
        $stmt->bindParam(2, $limit, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // 获取随机颜色
    private function getRandomColor() {
        $colors = ['红色', '橙色', '黄色', '绿色', '蓝色', '紫色', '粉色', '白色', '黑色', '金色'];
        return $colors[array_rand($colors)];
    }

    // 获取今日运势统计
    public function getTodayStats() {
        $query = "SELECT ft.name, COUNT(*) as count 
                  FROM " . $this->table_user_fortunes . " uf 
                  JOIN " . $this->table_types . " ft ON uf.fortune_type_id = ft.id 
                  WHERE DATE(uf.created_at) = CURDATE() 
                  GROUP BY ft.name 
                  ORDER BY count DESC";
        
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // 获取特定结果的详细信息
    public function getFortuneResult($result_id) {
        $query = "SELECT fr.*, ft.name as type_name 
                  FROM " . $this->table_results . " fr 
                  JOIN " . $this->table_types . " ft ON fr.type_id = ft.id 
                  WHERE fr.id = ?";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(1, $result_id);
        $stmt->execute();
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
}
?>
