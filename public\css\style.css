/* 基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Noto Sans SC', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* 头部样式 */
.header {
    text-align: center;
    margin-bottom: 40px;
    background: rgba(255, 255, 255, 0.95);
    padding: 30px;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.title {
    font-size: 2.5em;
    color: #4a5568;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.subtitle {
    font-size: 1.2em;
    color: #718096;
    font-weight: 300;
}

/* 主要内容区域 */
.main {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

/* 通用卡片样式 */
section {
    background: rgba(255, 255, 255, 0.95);
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(10px);
}

section h2 {
    color: #4a5568;
    margin-bottom: 20px;
    font-size: 1.5em;
    text-align: center;
}

/* 表单样式 */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #4a5568;
    font-weight: 500;
}

.form-group input {
    width: 100%;
    padding: 12px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s ease;
}

.form-group input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 占卜类型网格 */
.types-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.type-card {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
    padding: 25px;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    border: none;
    font-size: 16px;
}

.type-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
}

.type-card.selected {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    transform: scale(1.05);
}

.type-card h3 {
    font-size: 1.3em;
    margin-bottom: 10px;
}

.type-card p {
    font-size: 0.9em;
    opacity: 0.9;
}

/* 问题列表样式 */
.questions-list {
    margin-bottom: 30px;
}

.question-item {
    background: #f7fafc;
    padding: 20px;
    margin-bottom: 15px;
    border-radius: 10px;
    border-left: 4px solid #667eea;
}

.question-item h4 {
    color: #4a5568;
    margin-bottom: 15px;
    font-size: 1.1em;
}

.question-options {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.option-btn {
    padding: 8px 16px;
    border: 2px solid #e2e8f0;
    background: white;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
}

.option-btn:hover {
    border-color: #667eea;
    background: #667eea;
    color: white;
}

.option-btn.selected {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

/* 按钮样式 */
.fortune-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 25px;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    margin: 10px;
    min-width: 150px;
}

.fortune-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

.fortune-btn.secondary {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.fortune-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* 结果显示样式 */
.result-content {
    text-align: center;
    padding: 30px;
}

.result-header {
    margin-bottom: 30px;
}

.result-title {
    font-size: 2em;
    color: #4a5568;
    margin-bottom: 10px;
}

.luck-level {
    display: inline-block;
    padding: 8px 20px;
    border-radius: 20px;
    font-weight: bold;
    font-size: 1.1em;
    margin-bottom: 20px;
}

.luck-level.大吉 { background: #48bb78; color: white; }
.luck-level.中吉 { background: #38a169; color: white; }
.luck-level.小吉 { background: #68d391; color: white; }
.luck-level.吉 { background: #9ae6b4; color: #2d3748; }
.luck-level.半吉 { background: #fbb6ce; color: #2d3748; }
.luck-level.末吉 { background: #fed7d7; color: #2d3748; }
.luck-level.凶 { background: #fc8181; color: white; }
.luck-level.大凶 { background: #e53e3e; color: white; }

.result-content-text {
    font-size: 1.1em;
    line-height: 1.8;
    margin-bottom: 25px;
    color: #4a5568;
}

.result-advice {
    background: #edf2f7;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 25px;
    border-left: 4px solid #667eea;
}

.result-advice h4 {
    color: #4a5568;
    margin-bottom: 10px;
}

.result-extras {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    margin-top: 25px;
}

.extra-item {
    background: #f7fafc;
    padding: 15px;
    border-radius: 8px;
    text-align: center;
}

.extra-item strong {
    color: #667eea;
}

/* 历史记录样式 */
.history-list {
    max-height: 400px;
    overflow-y: auto;
}

.history-item {
    background: #f7fafc;
    padding: 15px;
    margin-bottom: 10px;
    border-radius: 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.history-info h4 {
    color: #4a5568;
    margin-bottom: 5px;
}

.history-info p {
    color: #718096;
    font-size: 0.9em;
}

/* 统计样式 */
.stats-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.stat-item {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 10px;
    text-align: center;
}

.stat-item h4 {
    margin-bottom: 10px;
}

.stat-number {
    font-size: 2em;
    font-weight: bold;
}

/* 加载动画 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.loading-spinner {
    text-align: center;
    color: white;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 页脚样式 */
.footer {
    text-align: center;
    margin-top: 40px;
    padding: 20px;
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9em;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .title {
        font-size: 2em;
    }
    
    .types-grid {
        grid-template-columns: 1fr;
    }
    
    .question-options {
        flex-direction: column;
    }
    
    .result-extras {
        grid-template-columns: 1fr;
    }
    
    .stats-content {
        grid-template-columns: 1fr;
    }
}
