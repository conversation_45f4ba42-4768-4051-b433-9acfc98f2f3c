#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import ftplib
import os

# FTP配置
FTP_HOST = 'ftp-myphp.heteml.net'
FTP_USER = 'myphp_yuzong'
FTP_PASS = 'gs888888'

def move_files_to_root():
    """将文件从yuzong目录移动到根目录"""
    
    try:
        # 连接FTP服务器
        print("连接FTP服务器...")
        ftp = ftplib.FTP(FTP_HOST)
        ftp.login(FTP_USER, FTP_PASS)
        print("✅ FTP连接成功")
        
        # 确保在根目录
        ftp.cwd('/')
        print(f"📍 当前目录: {ftp.pwd()}")
        
        # 直接上传process_chunks_on_server.php到根目录
        script_file = 'process_chunks_on_server.php'
        if os.path.exists(script_file):
            print(f"上传文件: {script_file} 到根目录")
            with open(script_file, 'rb') as file:
                ftp.storbinary(f'STOR {script_file}', file)
            print(f"✅ {script_file} 上传到根目录成功！")
        
        # 检查csv_chunks目录是否存在，如果不存在则创建
        try:
            ftp.mkd('csv_chunks')
            print("✅ 创建根目录下的 csv_chunks 目录")
        except:
            print("📁 根目录下的 csv_chunks 目录已存在")
        
        # 将所有CSV文件块从yuzong/csv_chunks移动到根目录的csv_chunks
        print("\n移动CSV文件块到根目录...")
        
        # 先检查yuzong/csv_chunks中的文件
        ftp.cwd('/yuzong/csv_chunks')
        yuzong_chunks = ftp.nlst()
        chunk_files = [f for f in yuzong_chunks if f.startswith('chunk_') and f.endswith('.csv')]
        
        print(f"在 /yuzong/csv_chunks/ 中找到 {len(chunk_files)} 个文件块")
        
        # 切换到根目录的csv_chunks
        ftp.cwd('/csv_chunks')
        
        # 复制前10个文件块作为测试
        for i, chunk_file in enumerate(chunk_files[:10]):
            print(f"复制: {chunk_file} ({i+1}/10)")
            
            # 下载文件内容
            ftp.cwd('/yuzong/csv_chunks')
            content = []
            ftp.retrlines(f'RETR {chunk_file}', content.append)
            
            # 上传到根目录
            ftp.cwd('/csv_chunks')
            with open(f'temp_{chunk_file}', 'w', encoding='utf-8') as temp_file:
                temp_file.write('\n'.join(content))
            
            with open(f'temp_{chunk_file}', 'rb') as temp_file:
                ftp.storbinary(f'STOR {chunk_file}', temp_file)
            
            # 删除临时文件
            os.remove(f'temp_{chunk_file}')
        
        print("✅ 测试文件块复制完成")
        
        # 关闭连接
        ftp.quit()
        
        print(f"\n🎉 文件移动完成！")
        print(f"现在可以访问: https://189527.com/yuzong/process_chunks_on_server.php")
        print(f"文件实际位置: FTP根目录")
        
        return True
        
    except Exception as e:
        print(f"❌ 移动失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("========================================")
    print("移动文件到FTP根目录")
    print("========================================")
    move_files_to_root() 