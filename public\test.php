<?php
// 简单的测试脚本，验证PHP和数据库连接
echo "<h1>🔮 神秘占卜馆 - 系统测试</h1>";

// 测试PHP版本
echo "<h2>PHP 版本信息</h2>";
echo "PHP 版本: " . phpversion() . "<br>";

// 测试扩展
echo "<h2>PHP 扩展检查</h2>";
echo "MySQLi 扩展: " . (extension_loaded('mysqli') ? '✅ 已安装' : '❌ 未安装') . "<br>";
echo "PDO 扩展: " . (extension_loaded('pdo') ? '✅ 已安装' : '❌ 未安装') . "<br>";
echo "PDO MySQL 扩展: " . (extension_loaded('pdo_mysql') ? '✅ 已安装' : '❌ 未安装') . "<br>";

// 测试数据库连接
echo "<h2>数据库连接测试</h2>";

try {
    $host = 'db';  // Docker内部网络中的主机名
    $dbname = 'fortune_db';
    $username = 'fortune_user';
    $password = 'fortune_pass';
    
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ 数据库连接成功！<br>";
    
    // 测试表是否存在
    $tables = ['fortune_types', 'fortune_results', 'user_fortunes', 'fortune_questions'];
    foreach ($tables as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            echo "✅ 表 $table 存在<br>";
        } else {
            echo "❌ 表 $table 不存在<br>";
        }
    }
    
    // 测试数据
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM fortune_types");
    $result = $stmt->fetch();
    echo "占卜类型数量: " . $result['count'] . "<br>";
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM fortune_results");
    $result = $stmt->fetch();
    echo "占卜结果数量: " . $result['count'] . "<br>";
    
} catch (PDOException $e) {
    echo "❌ 数据库连接失败: " . $e->getMessage() . "<br>";
}

echo "<h2>文件系统测试</h2>";
echo "当前目录: " . getcwd() . "<br>";
echo "index.php 文件: " . (file_exists('index.php') ? '✅ 存在' : '❌ 不存在') . "<br>";
echo "api/fortune.php 文件: " . (file_exists('api/fortune.php') ? '✅ 存在' : '❌ 不存在') . "<br>";
echo "css/style.css 文件: " . (file_exists('css/style.css') ? '✅ 存在' : '❌ 不存在') . "<br>";
echo "js/app.js 文件: " . (file_exists('js/app.js') ? '✅ 存在' : '❌ 不存在') . "<br>";

echo "<h2>环境变量</h2>";
echo "DB_HOST: " . ($_ENV['DB_HOST'] ?? '未设置') . "<br>";
echo "DB_NAME: " . ($_ENV['DB_NAME'] ?? '未设置') . "<br>";
echo "DB_USER: " . ($_ENV['DB_USER'] ?? '未设置') . "<br>";

echo "<hr>";
echo "<p><a href='index.php'>🔮 进入占卜游戏</a></p>";
echo "<p><a href='api/fortune.php?action=getTypes'>🔧 测试API接口</a></p>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
}

h1, h2 {
    color: #4a5568;
    background: rgba(255, 255, 255, 0.9);
    padding: 10px;
    border-radius: 5px;
}

p, div {
    background: rgba(255, 255, 255, 0.9);
    padding: 10px;
    border-radius: 5px;
    margin: 10px 0;
}

a {
    color: #667eea;
    text-decoration: none;
    font-weight: bold;
}

a:hover {
    text-decoration: underline;
}
</style>
