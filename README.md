# 🔮 神秘占卜馆 - Docker PHP MySQL 占卜游戏

一个基于Docker、PHP和MySQL的在线占卜游戏，提供多种占卜类型和个性化的占卜体验。

## ✨ 功能特色

- 🎯 **多种占卜类型**: 爱情运势、事业运势、财运、健康运势、学业运势、综合运势
- 🤔 **互动问答**: 通过回答问题获得更准确的占卜结果
- 📊 **个人记录**: 保存用户的占卜历史记录
- 📈 **统计分析**: 显示今日占卜统计数据
- 🎨 **精美界面**: 响应式设计，支持移动端访问
- 🔄 **实时更新**: 动态加载内容，流畅的用户体验

## 🛠️ 技术栈

- **后端**: PHP 8.1 + Apache
- **数据库**: MySQL 8.0
- **前端**: HTML5 + CSS3 + JavaScript (ES6+)
- **容器化**: Docker + Docker Compose
- **管理工具**: phpMyAdmin

## 📁 项目结构

```
占卜游戏/
├── docker-compose.yml          # Docker编排文件
├── Dockerfile                  # PHP容器配置
├── apache-config.conf          # Apache配置
├── database/                   # 数据库相关
│   ├── init.sql               # 数据库初始化脚本
│   └── seeds.sql              # 测试数据
├── config/                     # 配置文件
│   └── database.php           # 数据库连接配置
├── classes/                    # PHP类文件
│   └── Fortune.php            # 占卜逻辑类
└── public/                     # Web根目录
    ├── index.php              # 主页面
    ├── api/
    │   └── fortune.php        # API接口
    ├── css/
    │   └── style.css          # 样式文件
    └── js/
        └── app.js             # 前端JavaScript
```

## 🚀 快速开始

### 前置要求

- Docker
- Docker Compose

### 安装步骤

1. **克隆项目**
   ```bash
   git clone <项目地址>
   cd 占卜游戏
   ```

2. **启动服务**
   ```bash
   docker-compose up -d
   ```

3. **访问应用**
   - 占卜游戏: http://localhost:8082
   - phpMyAdmin: http://localhost:8081
     - 用户名: root
     - 密码: root_password

### 服务说明

- **web**: PHP应用服务器 (端口: 8082)
- **db**: MySQL数据库服务器 (端口: 3307)
- **phpmyadmin**: 数据库管理工具 (端口: 8081)

## 🎮 使用指南

### 基本流程

1. **输入个人信息** (可选)
   - 姓名: 用于保存占卜记录
   - 生日: 影响占卜结果

2. **选择占卜类型**
   - 爱情运势: 预测感情生活
   - 事业运势: 预测工作发展
   - 财运: 预测财富状况
   - 健康运势: 预测身体健康
   - 学业运势: 预测学习考试
   - 综合运势: 全面运势预测

3. **回答相关问题**
   - 系统会根据选择的类型提供3个相关问题
   - 每个问题有5个选项供选择

4. **获得占卜结果**
   - 查看详细的占卜内容
   - 获得建议和指导
   - 了解幸运数字和颜色

5. **查看历史记录**
   - 如果输入了姓名，可以查看历史占卜记录
   - 追踪运势变化趋势

## 🗄️ 数据库结构

### 主要数据表

- **fortune_types**: 占卜类型表
- **fortune_results**: 占卜结果表
- **user_fortunes**: 用户占卜记录表
- **fortune_questions**: 占卜问题表

### 运势等级

- 大吉 > 中吉 > 小吉 > 吉 > 半吉 > 末吉 > 末小吉 > 凶 > 小凶 > 半凶 > 末凶 > 大凶

## 🔧 开发说明

### API接口

所有API接口位于 `/api/fortune.php`，支持以下操作：

- `getTypes`: 获取占卜类型列表
- `getQuestions`: 获取指定类型的问题
- `getFortune`: 执行占卜并返回结果
- `getHistory`: 获取用户历史记录
- `getTodayStats`: 获取今日统计数据
- `getResult`: 获取指定结果详情

### 自定义配置

可以通过修改以下文件来自定义应用：

- `database/seeds.sql`: 添加更多占卜内容
- `public/css/style.css`: 修改界面样式
- `classes/Fortune.php`: 调整占卜逻辑

## 🛡️ 安全注意事项

- 数据库密码已在配置中设置，生产环境请修改
- API接口已添加基本的输入验证
- 建议在生产环境中添加更多安全措施

## 📝 许可证

本项目仅供学习和娱乐使用，请理性对待占卜结果。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 📞 支持

如果遇到问题，请查看：
1. Docker容器是否正常运行: `docker-compose ps`
2. 数据库是否正确初始化: 访问phpMyAdmin检查
3. 应用日志: `docker-compose logs web`

---

🎉 享受您的占卜之旅！
