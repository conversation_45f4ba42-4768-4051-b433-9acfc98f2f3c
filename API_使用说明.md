# 数据库API使用说明

## 概述

这是一个用于与MySQL数据库交互的RESTful API接口，支持Python客户端调用。

**API地址**: `https://189527.com/yuzong/api.php`

## 功能特性

✅ **已测试功能**:
- 连接测试
- 获取数据库信息
- 查询数据（支持分页、条件查询）
- 插入单条数据
- 更新数据
- 删除数据
- 搜索数据

⚠️ **需要调试功能**:
- 批量插入数据
- 执行自定义SQL（可能有权限限制）

## API接口列表

### 1. 测试连接
```
GET /api.php?action=test
```
**响应示例**:
```json
{
  "success": true,
  "message": "连接正常",
  "data": {
    "timestamp": "2025-05-27 13:50:29"
  }
}
```

### 2. 获取数据库信息
```
GET /api.php?action=database_info
```
**响应示例**:
```json
{
  "success": true,
  "message": "数据库信息获取成功",
  "data": {
    "tables": {
      "japanese_data": 157214,
      "users": 4,
      "products": 8
    },
    "total_tables": 9
  }
}
```

### 3. 查询数据
```
POST /api.php?action=query
```
**请求参数**:
```json
{
  "table": "users",
  "limit": 10,
  "offset": 0,
  "where": "status = 'active'"
}
```

### 4. 插入数据
```
POST /api.php?action=insert
```
**请求参数**:
```json
{
  "table": "users",
  "data": {
    "username": "newuser",
    "email": "<EMAIL>",
    "full_name": "新用户"
  }
}
```

### 5. 更新数据
```
POST /api.php?action=update
```
**请求参数**:
```json
{
  "table": "users",
  "data": {
    "full_name": "更新的用户名"
  },
  "where": "id = 1"
}
```

### 6. 删除数据
```
POST /api.php?action=delete
```
**请求参数**:
```json
{
  "table": "users",
  "where": "id = 1"
}
```

### 7. 搜索数据
```
POST /api.php?action=search
```
**请求参数**:
```json
{
  "table": "japanese_data",
  "keyword": "商品",
  "limit": 100
}
```

## Python客户端使用

### 安装依赖
```bash
pip install requests
```

### 基本使用
```python
from api_client import DatabaseAPI

# 创建API客户端
api = DatabaseAPI()

# 测试连接
api.test_connection()

# 获取数据库信息
api.get_database_info()

# 查询数据
users = api.query_data('users', limit=5)

# 插入数据
new_user = {
    'username': 'testuser',
    'email': '<EMAIL>',
    'full_name': '测试用户'
}
api.insert_data('users', new_user)

# 搜索数据
results = api.search_data('japanese_data', '商品', limit=10)
```

## 数据库表结构

### 当前数据表
1. **japanese_data** (157,214条记录) - 日文商品数据
2. **sku_controls** (87,596条记录) - SKU控制数据
3. **sku_products** (8,718条记录) - SKU产品数据
4. **sku_groups** (4,720条记录) - SKU组合数据
5. **users** (4条记录) - 用户数据
6. **products** (8条记录) - 产品数据
7. **orders** (0条记录) - 订单数据
8. **order_items** (0条记录) - 订单项数据
9. **test_japanese_products** (5条记录) - 测试日文产品数据

## 使用示例

### 1. 查询用户列表
```python
api = DatabaseAPI()
users = api.query_data('users', limit=10)
if users.get('success'):
    for user in users['data']['data']:
        print(f"用户: {user['username']} - {user['full_name']}")
```

### 2. 搜索日文商品
```python
results = api.search_data('japanese_data', '商品SKU', limit=5)
if results.get('success'):
    print(f"找到 {results['data']['found']} 条匹配记录")
    for item in results['data']['data']:
        print(f"SKU: {item.get('商品SKU', 'N/A')}")
```

### 3. 添加新用户
```python
new_user = {
    'username': 'newuser123',
    'email': '<EMAIL>',
    'full_name': '新用户',
    'phone': '13800138000',
    'status': 'active'
}
result = api.insert_data('users', new_user)
if result.get('success'):
    print(f"用户创建成功，ID: {result['data']['insert_id']}")
```

### 4. 分页查询大数据表
```python
# 查询第1页
page1 = api.query_data('japanese_data', limit=100, offset=0)

# 查询第2页
page2 = api.query_data('japanese_data', limit=100, offset=100)

# 查询总数
total = page1['data']['total'] if page1.get('success') else 0
print(f"总记录数: {total:,}")
```

## 错误处理

API返回的错误格式：
```json
{
  "success": false,
  "error": "错误信息"
}
```

Python客户端会自动处理网络错误和JSON解析错误。

## 注意事项

1. **安全性**: API已配置CORS，支持跨域访问
2. **编码**: 支持UTF-8编码，可以处理中文和日文数据
3. **性能**: 大数据查询建议使用分页
4. **限制**: 某些复杂SQL查询可能有权限限制

## 联系信息

- **网站**: https://189527.com/yuzong/
- **API测试**: https://189527.com/yuzong/api.php?action=test
- **数据库**: MySQL 8.3.21
- **服务器**: Apache

---

*最后更新: 2025-05-27* 