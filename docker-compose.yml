version: '3.8'

services:
  web:
    build: .
    ports:
      - "8082:80"
    volumes:
      - ./public:/var/www/html
      - ./config:/var/www/config
      - ./classes:/var/www/classes
    depends_on:
      - db
    environment:
      - DB_HOST=db
      - DB_NAME=fortune_db
      - DB_USER=fortune_user
      - DB_PASS=fortune_pass
    networks:
      - fortune-network

  db:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: root_password
      MYSQL_DATABASE: fortune_db
      MYSQL_USER: fortune_user
      MYSQL_PASSWORD: fortune_pass
    ports:
      - "3307:3306"
    volumes:
      - ./database:/docker-entrypoint-initdb.d
      - mysql_data:/var/lib/mysql
    networks:
      - fortune-network

  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    ports:
      - "8081:80"
    environment:
      PMA_HOST: db
      PMA_USER: root
      PMA_PASSWORD: root_password
    depends_on:
      - db
    networks:
      - fortune-network

volumes:
  mysql_data:

networks:
  fortune-network:
    driver: bridge
